/**
 * Better-auth API route handler for Remix
 * 
 * This route handles all authentication endpoints like:
 * - /api/auth/sign-in
 * - /api/auth/sign-up
 * - /api/auth/sign-out
 * - /api/auth/callback/*
 * And other better-auth endpoints
 */

import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { auth } from "~/lib/auth.server";

export async function loader({ request }: LoaderFunctionArgs) {
  console.log('[Auth Route] Loader called for:', request.url);
  try {
    const result = await auth.handler(request);
    console.log('[Auth Route] Loader result:', result?.status || 'no status');
    return result;
  } catch (error) {
    console.error('[Auth Route] Loader error:', error);
    throw error;
  }
}

export async function action({ request }: ActionFunctionArgs) {
  console.log('[Auth Route] Action called for:', request.url, request.method);
  try {
    const result = await auth.handler(request);
    console.log('[Auth Route] Action result:', result?.status || 'no status');
    return result;
  } catch (error) {
    console.error('[Auth Route] Action error:', error);
    throw error;
  }
}