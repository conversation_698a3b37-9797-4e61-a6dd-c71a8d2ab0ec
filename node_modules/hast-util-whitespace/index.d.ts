/**
 * Check if the given value is *inter-element whitespace*.
 *
 * @param {unknown} thing
 *   Thing to check (typically `Node` or `string`).
 * @returns {boolean}
 *   Whether the `value` is inter-element whitespace (`boolean`): consisting of
 *   zero or more of space, tab (`\t`), line feed (`\n`), carriage return
 *   (`\r`), or form feed (`\f`).
 *   If a node is passed it must be a `Text` node, whose `value` field is
 *   checked.
 */
export function whitespace(thing: unknown): boolean
