# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [0.1.1](https://github.com/remarkablemark/inline-style-parser/compare/v0.1.0...v0.1.1) (2019-06-20)


### Build System

* **package:** fix whitelisting of `/dist` in "files" field ([2c13b2f](https://github.com/remarkablemark/inline-style-parser/commit/2c13b2f))



## 0.1.0 (2019-06-19)


### Bug Fixes

* **index:** do not throw an error if a comment precedes the colon ([7f962ee](https://github.com/remarkablemark/inline-style-parser/commit/7f962ee))


### Build System

* **package:** add `build` and `clean` scripts ([d27a653](https://github.com/remarkablemark/inline-style-parser/commit/d27a653))
* **package:** add script `prepublishOnly` and "files" field ([5fad9ff](https://github.com/remarkablemark/inline-style-parser/commit/5fad9ff))
* **package:** save `css@2.2.4` to devDependencies ([93ad729](https://github.com/remarkablemark/inline-style-parser/commit/93ad729))
* **package:** save devDependencies for `rollup` and its plugins ([872b1fa](https://github.com/remarkablemark/inline-style-parser/commit/872b1fa))
* **package:** set `NODE_ENV=development` in script `build:unmin` ([5a7877b](https://github.com/remarkablemark/inline-style-parser/commit/5a7877b))
* **package:** update `build:min` to generate sourcemap (external) ([c81d66a](https://github.com/remarkablemark/inline-style-parser/commit/c81d66a))
* **rollup:** add `rollup.config.js` ([ac60124](https://github.com/remarkablemark/inline-style-parser/commit/ac60124))


### Features

* clone project from `npm-package-template` ([5976c6f](https://github.com/remarkablemark/inline-style-parser/commit/5976c6f))
* **index:** copy `parse` module from `css` package ([3bf4bee](https://github.com/remarkablemark/inline-style-parser/commit/3bf4bee))
* **index:** parse only declarations and remove all unused code ([a04d918](https://github.com/remarkablemark/inline-style-parser/commit/a04d918))
* **index:** throw error if first argument is not a string ([346ae28](https://github.com/remarkablemark/inline-style-parser/commit/346ae28))


### Tests

* add snapshot for the parsed output of a single declaration ([c2c774c](https://github.com/remarkablemark/inline-style-parser/commit/c2c774c))
* **data:** add more cases for 'content' and 'background-image' ([204c574](https://github.com/remarkablemark/inline-style-parser/commit/204c574))
* **index:** add more misc and one-off test cases ([a08f521](https://github.com/remarkablemark/inline-style-parser/commit/a08f521))
* **index:** check that a comment before colon is parsed correctly ([bf9518c](https://github.com/remarkablemark/inline-style-parser/commit/bf9518c))
* **index:** check that the error message matches ([9169525](https://github.com/remarkablemark/inline-style-parser/commit/9169525))
* add snapshots for the parsed output of multiple declarations ([8708031](https://github.com/remarkablemark/inline-style-parser/commit/8708031))
* **index:** disable placeholder test suite ([20bf8af](https://github.com/remarkablemark/inline-style-parser/commit/20bf8af))
* add cases and compare parser output with `css.parse` output ([361974b](https://github.com/remarkablemark/inline-style-parser/commit/361974b))
* **index:** refactor tests and use `expect` and `it.each` ([dbf2ef0](https://github.com/remarkablemark/inline-style-parser/commit/dbf2ef0))
* organize tests with describe blocks and tidy test names ([5c5fcd4](https://github.com/remarkablemark/inline-style-parser/commit/5c5fcd4))
* replace `mocha` and `nyc` with `jest` ([100311d](https://github.com/remarkablemark/inline-style-parser/commit/100311d))
* **index:** test "End of comment missing" error and silent option ([9f0c832](https://github.com/remarkablemark/inline-style-parser/commit/9f0c832))
* **index:** verify that expected errors are thrown ([d0ed3bd](https://github.com/remarkablemark/inline-style-parser/commit/d0ed3bd))
* **package:** collect coverage from `index.js` only ([bc503b7](https://github.com/remarkablemark/inline-style-parser/commit/bc503b7))
