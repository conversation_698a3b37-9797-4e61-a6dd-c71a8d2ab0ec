"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeadersInstanceToPlainObject = exports.uppercase = void 0;
const uppercase = (str) => str.toUpperCase();
exports.uppercase = uppercase;
/**
 * Convert Headers instance into regular object
 */
const HeadersInstanceToPlainObject = (headers) => {
    const o = {};
    headers.forEach((v, k) => {
        o[k] = v;
    });
    return o;
};
exports.HeadersInstanceToPlainObject = HeadersInstanceToPlainObject;
//# sourceMappingURL=helpers.js.map