{"version": 3, "file": "parseArgs.js", "sourceRoot": "", "sources": ["../../src/parseArgs.ts"], "names": [], "mappings": "AAaA,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAC9B,iBAAsD,EACtD,SAAa,EACb,cAA4C,EACzB,EAAE;IACrB,OAAQ,iBAAuC,CAAC,QAAQ;QACtD,CAAC,CAAE,iBAAuC;QAC1C,CAAC,CAAE;YACC,QAAQ,EAAE,iBAAoC;YAC9C,SAAS,EAAE,SAAS;YACpB,cAAc,EAAE,cAAc;YAC9B,MAAM,EAAE,SAAS;SACe,CAAA;AACxC,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,CACjC,cAA6C,EAC7C,SAAa,EACb,cAA4C,EACtB,EAAE;IACxB,OAAQ,cAAuC,CAAC,KAAK;QACnD,CAAC,CAAE,cAAuC;QAC1C,CAAC,CAAE;YACC,KAAK,EAAE,cAAwB;YAC/B,SAAS,EAAE,SAAS;YACpB,cAAc,EAAE,cAAc;YAC9B,MAAM,EAAE,SAAS;SACkB,CAAA;AAC3C,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,CACnC,kBAAuE,EACvE,cAA4C,EACnB,EAAE;IAC3B,OAAQ,kBAA8C,CAAC,SAAS;QAC9D,CAAC,CAAE,kBAA8C;QACjD,CAAC,CAAC;YACE,SAAS,EAAE,kBAA+C;YAC1D,cAAc,EAAE,cAAc;YAC9B,MAAM,EAAE,SAAS;SAClB,CAAA;AACP,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,wBAAwB,GAAG,CACtC,YAAgD,EAChD,QAA0B,EAC1B,GAAG,0BAA6D,EACrC,EAAE;IAC7B,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,GAAG,0BAA0B,CAAA;IAC9D,OAAQ,YAA0C,CAAC,QAAQ;QACzD,CAAC,CAAE,YAA0C;QAC7C,CAAC,CAAE;YACC,GAAG,EAAE,YAAsB;YAC3B,QAAQ,EAAE,QAA2B;YACrC,SAAS;YACT,cAAc;YACd,MAAM,EAAE,SAAS;SACuB,CAAA;AAChD,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,2BAA2B,GAAG,CACzC,YAAmD,EACnD,KAAc,EACd,GAAG,0BAA6D,EAClC,EAAE;IAChC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,GAAG,0BAA0B,CAAA;IAC9D,OAAQ,YAA6C,CAAC,KAAK;QACzD,CAAC,CAAE,YAA6C;QAChD,CAAC,CAAE;YACC,GAAG,EAAE,YAAsB;YAC3B,KAAK,EAAE,KAAe;YACtB,SAAS;YACT,cAAc;YACd,MAAM,EAAE,SAAS;SAC0B,CAAA;AACnD,CAAC,CAAA"}