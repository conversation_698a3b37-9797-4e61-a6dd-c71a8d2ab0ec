{"version": 3, "file": "graphql-ws.d.ts", "sourceRoot": "", "sources": ["../../src/graphql-ws.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,YAAY,CAAA;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAA;AAuDxC,MAAM,MAAM,aAAa,GAAG;IAC1B,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAA;IAC5B,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA;IAClD,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;IAC/C,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,KAAK,GAAG,CAAA;IAC/B,OAAO,CAAC,EAAE,MAAM,GAAG,CAAA;CACpB,CAAA;AAED,MAAM,MAAM,mBAAmB,GAAG,MAAM,IAAI,CAAA;AAE5C,MAAM,WAAW,iBAAiB,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO;IAC/C,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;IACpC,KAAK,CAAC,CAAC,UAAU,EAAE,WAAW,GAAG,IAAI,CAAA;IACrC,QAAQ,CAAC,IAAI,IAAI,CAAA;CAClB;AAcD,qBAAa,sBAAsB;IACjC,MAAM,CAAC,QAAQ,SAAyB;IAExC,OAAO,CAAC,MAAM,CAAW;IACzB,OAAO,CAAC,WAAW,CAA4E;gBAEnF,MAAM,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,aAAa;IAyFxF,OAAO,CAAC,aAAa;IAerB,UAAU,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG,EAC1D,KAAK,EAAE,MAAM,EACb,SAAS,CAAC,EAAE,CAAC,GACZ,OAAO,CAAC;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,UAAU,CAAC,EAAE,CAAC,CAAA;KAAE,CAAC;IAevC,OAAO,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAevG,SAAS,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG,EACzD,QAAQ,EAAE,eAAe,EACzB,UAAU,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EACnC,SAAS,CAAC,EAAE,CAAC,GACZ,mBAAmB;IAKtB,YAAY,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG,EAC5D,KAAK,EAAE,MAAM,EACb,UAAU,EAAE,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,EACnC,SAAS,CAAC,EAAE,CAAC,GACZ,mBAAmB;IAItB,IAAI,CAAC,OAAO,EAAE,SAAS;IAIvB,KAAK;CAGN"}