{"name": "graphql-request", "version": "6.1.0", "type": "module", "main": "./build/cjs/index.js", "exports": {".": {"require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}, "import": {"types": "./build/esm/index.d.ts", "default": "./build/esm/index.js"}}}, "types": "./build/esm/index.d.ts", "packageManager": "pnpm@8.5.1", "files": ["build", "src"], "repository": {"type": "git", "url": "https://github.com/jasonkuhrt/graphql-request.git"}, "keywords": ["graphql", "request", "fetch", "graphql-client"], "author": {"name": "<PERSON>", "email": "jason<PERSON><PERSON><EMAIL>", "url": "https://kuhrt.me"}, "license": "MIT", "bugs": {"url": "https://github.com/jasonkuhrt/graphql-request/issues"}, "homepage": "https://github.com/jasonkuhrt/graphql-request", "scripts": {"dev": "rm -rf dist && tsc --watch", "format": "pnpm build:docs && prettier --write .", "lint": "eslint . --ext .ts,.tsx --fix", "check": "pnpm check:types && pnpm check:format && pnpm check:lint", "check:types": "pnpm tsc --noEmit", "check:format": "prettier --check . && pnpm build:docs && git diff --exit-code README.md", "check:lint": "eslint . --ext .ts,.tsx --max-warnings 0", "prepublishOnly": "pnpm build", "build:docs": "doctoc README.md --notitle && prettier --write README.md", "build": "pnpm clean && pnpm build:cjs && pnpm build:esm", "build:cjs": "pnpm tsc --project tsconfig.cjs.json && echo '{\"type\":\"commonjs\"}' > build/cjs/package.json", "build:esm": "pnpm tsc --project tsconfig.esm.json", "clean": "tsc --build --clean && rm -rf build", "test": "vitest", "test:coverage": "pnpm test -- --coverage", "release:stable": "dripip stable", "release:preview": "dripip preview", "release:pr": "dripip pr"}, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "cross-fetch": "^3.1.5"}, "peerDependencies": {"graphql": "14 - 16"}, "devDependencies": {"@graphql-tools/schema": "^9.0.18", "@prisma-labs/prettier-config": "^0.1.0", "@tsconfig/node16": "^1.0.3", "@types/body-parser": "^1.19.2", "@types/express": "^4.17.17", "@types/json-bigint": "^1.0.1", "@types/node": "^20.0.0", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vitest/coverage-c8": "^0.31.0", "apollo-server-express": "^3.12.0", "body-parser": "^1.20.2", "doctoc": "^2.2.1", "dripip": "^0.10.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-config-prisma": "^0.1.0", "eslint-plugin-deprecation": "^1.4.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tsdoc": "^0.2.17", "express": "^4.18.2", "fetch-cookie": "^2.1.0", "get-port": "^6.1.2", "graphql": "^16.6.0", "graphql-tag": "^2.12.6", "graphql-ws": "^5.12.1", "happy-dom": "^9.7.1", "json-bigint": "^1.0.0", "prettier": "^2.8.7", "type-fest": "^3.8.0", "typescript": "^5.0.4", "vitest": "^0.31.0", "ws": "^8.13.0"}, "prettier": "@prisma-labs/prettier-config"}