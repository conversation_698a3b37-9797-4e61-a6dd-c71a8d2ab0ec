{"name": "estree-util-attach-comments", "version": "2.1.1", "description": "Atta<PERSON> comments to estree nodes", "license": "MIT", "keywords": ["estree", "ast", "ecmascript", "javascript", "tree", "comment", "acorn", "espree", "recast"], "repository": "syntax-tree/estree-util-attach-comments", "bugs": "https://github.com/syntax-tree/estree-util-attach-comments/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/estree": "^1.0.0"}, "devDependencies": {"@types/acorn": "^4.0.0", "@types/node": "^18.0.0", "acorn": "^8.0.0", "c8": "^7.0.0", "estree-util-visit": "^1.0.0", "prettier": "^2.0.0", "recast": "^0.22.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.53.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"max-depth": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true}}